AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: GameFlex Backend - SAM Template for LocalStack Development

Parameters:
  Environment:
    Type: String
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    Description: Environment name

  ProjectName:
    Type: String
    Default: gameflex
    Description: Project name for resource naming

  R2AccountId:
    Type: String
    Default: ""
    Description: CloudFlare R2 Account ID (optional)

  R2AccessKeyId:
    Type: String
    Default: ""
    Description: CloudFlare R2 Access Key ID (optional)

  R2SecretAccessKey:
    Type: String
    Default: ""
    Description: CloudFlare R2 Secret Access Key (optional)

  R2Endpoint:
    Type: String
    Default: ""
    Description: CloudFlare R2 Endpoint URL (optional)

  R2BucketName:
    Type: String
    Default: gameflex-development
    Description: CloudFlare R2 Bucket Name

  R2PublicUrl:
    Type: String
    Default: "https://pub-34709f09e8384ef1a67928492571c01d.r2.dev"
    Description: CloudFlare R2 Public URL (optional)

Globals:
  Function:
    Runtime: nodejs20.x
    Timeout: 30
    MemorySize: 256
    Environment:
      Variables:
        ENVIRONMENT: !Ref Environment
        PROJECT_NAME: !Ref ProjectName
        USER_POOL_ID: !Ref UserPool
        USER_POOL_CLIENT_ID: !Ref UserPoolClient
        POSTS_TABLE: !Ref PostsTable
        MEDIA_TABLE: !Ref MediaTable
        USER_PROFILES_TABLE: !Ref UserProfilesTable
        COMMENTS_TABLE: !Ref CommentsTable
        LIKES_TABLE: !Ref LikesTable
        FOLLOWS_TABLE: !Ref FollowsTable
        CHANNELS_TABLE: !Ref ChannelsTable
        CHANNEL_MEMBERS_TABLE: !Ref ChannelMembersTable
        REFLEXES_TABLE: !Ref ReflexesTable
        # Note: S3 buckets removed - using CloudFlare R2 instead
        USERS_TABLE: !Ref UsersTable
        # CloudFlare R2 Configuration
        R2_ACCOUNT_ID: !Ref R2AccountId
        R2_ACCESS_KEY_ID: !Ref R2AccessKeyId
        R2_SECRET_ACCESS_KEY: !Ref R2SecretAccessKey
        R2_ENDPOINT: !Ref R2Endpoint
        R2_BUCKET_NAME: !Ref R2BucketName
        R2_PUBLIC_URL: !Ref R2PublicUrl

Resources:
  # Note: S3 buckets removed - using CloudFlare R2 instead

  # API Gateway with Lambda Authorizer
  ApiGateway:
    Type: AWS::Serverless::Api
    Properties:
      StageName: Dev
      Cors:
        AllowMethods: "'GET,POST,PUT,DELETE,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      Auth:
        Authorizers:
          CognitoAuthorizer:
            FunctionArn: !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${ProjectName}-authorizer-${Environment}"
            FunctionPayloadType: TOKEN
            Identity:
              Header: Authorization

  PostsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Posts
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  MediaTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Media
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  UserProfilesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-UserProfiles
      AttributeDefinitions:
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: user_id
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST

  CommentsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Comments
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: post_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: post-id-index
          KeySchema:
            - AttributeName: post_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  LikesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Likes
      AttributeDefinitions:
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: post_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: user-id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  FollowsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Follows
      AttributeDefinitions:
        - AttributeName: follower_id
          AttributeType: S
        - AttributeName: following_id
          AttributeType: S
      KeySchema:
        - AttributeName: follower_id
          KeyType: HASH
        - AttributeName: following_id
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST

  ChannelsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Channels
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: owner_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: owner-id-index
          KeySchema:
            - AttributeName: owner_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  ChannelMembersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-ChannelMembers
      AttributeDefinitions:
        - AttributeName: channel_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: channel_id
          KeyType: HASH
        - AttributeName: user_id
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: user-id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  ReflexesTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Reflexes
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: post_id
          AttributeType: S
        - AttributeName: user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: post-id-index
          KeySchema:
            - AttributeName: post_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: user-id-index
          KeySchema:
            - AttributeName: user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

  # Cognito User Pool
  UserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: !Sub ${ProjectName}-users-${Environment}
      AutoVerifiedAttributes:
        - email
      UsernameAttributes:
        - email
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: false

  UserPoolClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      ClientName: !Sub ${ProjectName}-client-${Environment}
      UserPoolId: !Ref UserPool
      GenerateSecret: false
      ExplicitAuthFlows:
        - ALLOW_ADMIN_USER_PASSWORD_AUTH
        - ALLOW_USER_PASSWORD_AUTH
        - ALLOW_REFRESH_TOKEN_AUTH
      TokenValidityUnits:
        RefreshToken: days
        AccessToken: minutes
        IdToken: minutes
      RefreshTokenValidity: 30
      AccessTokenValidity: 60
      IdTokenValidity: 60

  # Lambda Functions
  AuthorizerFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-authorizer-${Environment}
      CodeUri: src/authorizer/
      Handler: index.handler
      Runtime: nodejs18.x
      Environment:
        Variables:
          USER_POOL_ID: !Ref UserPool
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:GetUser
              Resource: !GetAtt UserPool.Arn

  AuthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-auth-${Environment}
      CodeUri: src/auth/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:*
              Resource: !GetAtt UserPool.Arn
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - Statement:
            - Effect: Allow
              Action:
                - cognito-idp:Admin*
                - cognito-idp:DescribeIdentityProvider
                - cognito-idp:DescribeResourceServer
                - cognito-idp:DescribeUserPool
                - cognito-idp:DescribeUserPoolClient
                - cognito-idp:DescribeUserPoolDomain
                - cognito-idp:GetGroup
                - cognito-idp:ListGroups
                - cognito-idp:ListUserPoolClients
                - cognito-idp:ListUsers
                - cognito-idp:ListUsersInGroup
                - cognito-idp:UpdateGroup
              Resource: !GetAtt UserPool.Arn
      Events:
        AuthSignin:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/signin
            Method: POST
        AuthSignup:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/signup
            Method: POST
        AuthRefresh:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/refresh
            Method: POST
        AuthValidate:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /auth/validate
            Method: GET

  PostsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-posts-${Environment}
      CodeUri: src/posts/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref PostsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
        - DynamoDBCrudPolicy:
            TableName: !Ref CommentsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref LikesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref ReflexesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        # Note: S3 policies removed - using CloudFlare R2 instead
      Events:
        GetPosts:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        CreateDraftPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/draft
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        CreatePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        GetPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}
            Method: GET
        AttachMediaToPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/media
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        PublishPost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/publish
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        UpdatePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        DeletePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        LikePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/like
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        UnlikePost:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/like
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        GetComments:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/comments
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        CreateComment:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/comments
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        UpdateComment:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /comments/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        DeleteComment:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /comments/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer

  MediaFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-media-${Environment}
      CodeUri: src/media/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
        # Note: S3 policies removed - using CloudFlare R2 instead
      Events:
        UploadMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/upload
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        GetMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/{id}
            Method: GET
        DeleteMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        UpdateMedia:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /media/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer

  UsersFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-users-${Environment}
      CodeUri: src/users/
      Handler: index.handler
      Environment:
        Variables:
          USERS_TABLE: !Ref UsersTable
          USER_PROFILES_TABLE: !Ref UserProfilesTable
          FOLLOWS_TABLE: !Ref FollowsTable
          POSTS_TABLE: !Ref PostsTable
          LIKES_TABLE: !Ref LikesTable
          USER_POOL_ID: !Ref UserPool
          USER_POOL_CLIENT_ID: !Ref UserPoolClient
          REGION: !Ref AWS::Region
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UserProfilesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref FollowsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref PostsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref LikesTable
        # Note: S3 policies removed - using CloudFlare R2 instead
      Events:
        GetProfile:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/profile
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        UpdateProfile:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/profile
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        GetUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{id}
            Method: GET
        FollowUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{id}/follow
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        UnfollowUser:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/{id}/follow
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        GetUserLikedPosts:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /users/profile/liked-posts
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer

  HealthFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-health-${Environment}
      CodeUri: src/health/
      Handler: index.handler
      Events:
        HealthCheck:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /health
            Method: GET
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable

  ReflexesFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-reflexes-${Environment}
      CodeUri: src/reflexes/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref ReflexesTable
        - DynamoDBCrudPolicy:
            TableName: !Ref PostsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
      Events:
        GetReflexes:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/reflexes
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        CreateReflex:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /posts/{id}/reflexes
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        DeleteReflex:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /reflexes/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer

  ChannelsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub ${ProjectName}-channels-${Environment}
      CodeUri: src/channels/
      Handler: index.handler
      Policies:
        - DynamoDBCrudPolicy:
            TableName: !Ref ChannelsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref ChannelMembersTable
        - DynamoDBCrudPolicy:
            TableName: !Ref UsersTable
        - DynamoDBCrudPolicy:
            TableName: !Ref PostsTable
        - DynamoDBCrudPolicy:
            TableName: !Ref MediaTable
      Events:
        GetChannels:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        CreateChannel:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        GetChannel:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels/{id}
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer
        UpdateChannel:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels/{id}
            Method: PUT
            Auth:
              Authorizer: CognitoAuthorizer
        DeleteChannel:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels/{id}
            Method: DELETE
            Auth:
              Authorizer: CognitoAuthorizer
        JoinChannel:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels/{id}/join
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        LeaveChannel:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels/{id}/leave
            Method: POST
            Auth:
              Authorizer: CognitoAuthorizer
        GetChannelPosts:
          Type: Api
          Properties:
            RestApiId: !Ref ApiGateway
            Path: /channels/{id}/posts
            Method: GET
            Auth:
              Authorizer: CognitoAuthorizer

  # DynamoDB Tables
  UsersTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub ${ProjectName}-${Environment}-Users
      AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        - AttributeName: email
          AttributeType: S
        - AttributeName: username
          AttributeType: S
        - AttributeName: cognito_user_id
          AttributeType: S
      KeySchema:
        - AttributeName: id
          KeyType: HASH
      GlobalSecondaryIndexes:
        - IndexName: EmailIndex
          KeySchema:
            - AttributeName: email
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: UsernameIndex
          KeySchema:
            - AttributeName: username
              KeyType: HASH
          Projection:
            ProjectionType: ALL
        - IndexName: CognitoUserIdIndex
          KeySchema:
            - AttributeName: cognito_user_id
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      BillingMode: PAY_PER_REQUEST

Outputs:
  ApiGatewayUrl:
    Description: API Gateway endpoint URL
    Value: !Sub https://${ApiGateway}.execute-api.${AWS::Region}.amazonaws.com/Dev/
    Export:
      Name: !Sub ${ProjectName}-api-url-${Environment}

  UserPoolId:
    Description: Cognito User Pool ID
    Value: !Ref UserPool
    Export:
      Name: !Sub ${ProjectName}-user-pool-id-${Environment}

  UserPoolClientId:
    Description: Cognito User Pool Client ID
    Value: !Ref UserPoolClient
    Export:
      Name: !Sub ${ProjectName}-user-pool-client-id-${Environment}

  # Note: S3 bucket outputs removed - using CloudFlare R2 instead

  UsersTableName:
    Description: DynamoDB Users Table Name
    Value: !Ref UsersTable
    Export:
      Name: !Sub ${ProjectName}-users-table-${Environment}

  PostsTableName:
    Description: DynamoDB Posts Table Name
    Value: !Ref PostsTable
    Export:
      Name: !Sub ${ProjectName}-posts-table-${Environment}

  MediaTableName:
    Description: DynamoDB Media Table Name
    Value: !Ref MediaTable
    Export:
      Name: !Sub ${ProjectName}-media-table-${Environment}

  UserProfilesTableName:
    Description: DynamoDB UserProfiles Table Name
    Value: !Ref UserProfilesTable
    Export:
      Name: !Sub ${ProjectName}-user-profiles-table-${Environment}

  CommentsTableName:
    Description: DynamoDB Comments Table Name
    Value: !Ref CommentsTable
    Export:
      Name: !Sub ${ProjectName}-comments-table-${Environment}

  LikesTableName:
    Description: DynamoDB Likes Table Name
    Value: !Ref LikesTable
    Export:
      Name: !Sub ${ProjectName}-likes-table-${Environment}

  FollowsTableName:
    Description: DynamoDB Follows Table Name
    Value: !Ref FollowsTable
    Export:
      Name: !Sub ${ProjectName}-follows-table-${Environment}

  ReflexesTableName:
    Description: DynamoDB Reflexes Table Name
    Value: !Ref ReflexesTable
    Export:
      Name: !Sub ${ProjectName}-reflexes-table-${Environment}
