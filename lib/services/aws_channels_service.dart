import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/channel_model.dart';
import '../models/post_model.dart';
import '../utils/app_logger.dart';
import 'config_service.dart';
import 'aws_auth_service.dart';

class AwsChannelsService {
  static final AwsChannelsService _instance = AwsChannelsService._internal();
  factory AwsChannelsService() => _instance;
  static AwsChannelsService get instance => _instance;
  AwsChannelsService._internal();

  final ConfigService _configService = ConfigService.instance;
  final AwsAuthService _authService = AwsAuthService.instance;

  Future<String> get _baseUrl => _configService.getServerUrl();

  Future<Map<String, String>> _getHeaders() async {
    final token = _authService.accessToken;
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Get channels with pagination
  Future<List<ChannelModel>> getChannels({
    int limit = 20,
    String? nextOffset,
  }) async {
    try {
      developer.log('AwsChannelsService: Getting channels with limit: $limit');

      final headers = await _getHeaders();
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };

      if (nextOffset != null) {
        queryParams['offset'] = nextOffset;
      }

      final baseUrl = await _baseUrl;
      final uri = Uri.parse('$baseUrl/channels').replace(
        queryParameters: queryParams,
      );

      developer.log('AwsChannelsService: Making request to: $uri');

      final response = await http.get(uri, headers: headers);

      developer.log(
        'AwsChannelsService: Response status: ${response.statusCode}',
      );
      developer.log(
        'AwsChannelsService: Response body: ${response.body}',
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final channelsData = data['channels'] as List<dynamic>;

        final channels = channelsData
            .map((channelJson) => ChannelModel.fromJson(channelJson))
            .toList();

        developer.log(
          'AwsChannelsService: Successfully loaded ${channels.length} channels',
        );

        return channels;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          'Failed to load channels: ${errorData['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e, stackTrace) {
      developer.log(
        'AwsChannelsService: Error getting channels',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('AwsChannelsService ERROR', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get a specific channel by ID
  Future<ChannelModel> getChannel(String channelId) async {
    try {
      developer.log('AwsChannelsService: Getting channel: $channelId');

      final headers = await _getHeaders();
      final baseUrl = await _baseUrl;
      final uri = Uri.parse('$baseUrl/channels/$channelId');

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final channel = ChannelModel.fromJson(data);

        developer.log(
          'AwsChannelsService: Successfully loaded channel: ${channel.name}',
        );

        return channel;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          'Failed to load channel: ${errorData['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e, stackTrace) {
      developer.log(
        'AwsChannelsService: Error getting channel',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create a new channel
  Future<ChannelModel> createChannel({
    required String name,
    String? description,
    bool isPublic = true,
  }) async {
    try {
      developer.log('AwsChannelsService: Creating channel: $name');

      final headers = await _getHeaders();
      final baseUrl = await _baseUrl;
      final uri = Uri.parse('$baseUrl/channels');

      final body = json.encode({
        'name': name,
        'description': description,
        'isPublic': isPublic,
      });

      final response = await http.post(uri, headers: headers, body: body);

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        final channel = ChannelModel.fromJson(data);

        developer.log(
          'AwsChannelsService: Successfully created channel: ${channel.name}',
        );

        return channel;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          'Failed to create channel: ${errorData['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e, stackTrace) {
      developer.log(
        'AwsChannelsService: Error creating channel',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Join a channel
  Future<void> joinChannel(String channelId) async {
    try {
      developer.log('AwsChannelsService: Joining channel: $channelId');

      final headers = await _getHeaders();
      final baseUrl = await _baseUrl;
      final uri = Uri.parse('$baseUrl/channels/$channelId/join');

      final response = await http.post(uri, headers: headers);

      if (response.statusCode == 200) {
        developer.log(
          'AwsChannelsService: Successfully joined channel: $channelId',
        );
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          'Failed to join channel: ${errorData['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e, stackTrace) {
      developer.log(
        'AwsChannelsService: Error joining channel',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Leave a channel
  Future<void> leaveChannel(String channelId) async {
    try {
      developer.log('AwsChannelsService: Leaving channel: $channelId');

      final headers = await _getHeaders();
      final baseUrl = await _baseUrl;
      final uri = Uri.parse('$baseUrl/channels/$channelId/leave');

      final response = await http.post(uri, headers: headers);

      if (response.statusCode == 200) {
        developer.log(
          'AwsChannelsService: Successfully left channel: $channelId',
        );
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          'Failed to leave channel: ${errorData['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e, stackTrace) {
      developer.log(
        'AwsChannelsService: Error leaving channel',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get posts for a specific channel
  Future<List<PostModel>> getChannelPosts({
    required String channelId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      developer.log(
        'AwsChannelsService: Getting posts for channel: $channelId',
      );

      final headers = await _getHeaders();
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      final baseUrl = await _baseUrl;
      final uri = Uri.parse('$baseUrl/channels/$channelId/posts').replace(
        queryParameters: queryParams,
      );

      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final postsData = data['posts'] as List<dynamic>;

        final posts = postsData
            .map((postJson) => PostModel.fromJson(postJson))
            .toList();

        developer.log(
          'AwsChannelsService: Successfully loaded ${posts.length} posts for channel: $channelId',
        );

        return posts;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          'Failed to load channel posts: ${errorData['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e, stackTrace) {
      developer.log(
        'AwsChannelsService: Error getting channel posts',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
