import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../utils/app_logger.dart';
import 'api_service.dart';
import 'aws_auth_service.dart';

/// AWS media service for handling R2 uploads via API Gateway
class AwsMediaService {
  static AwsMediaService? _instance;
  static AwsMediaService get instance => _instance ??= AwsMediaService._();

  AwsMediaService._();

  /// Upload media file to R2 using presigned URL
  Future<String?> uploadMedia({
    required File file,
    required String fileName,
    required String fileType,
    String mediaType = 'image',
  }) async {
    try {
      developer.log('AwsMediaService: Starting media upload for $fileName');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      // Step 1: Request upload URL from backend
      final uploadUrlResponse = await _requestUploadUrl(
        fileName: fileName,
        fileType: fileType,
        fileSize: await file.length(),
        mediaType: mediaType,
        accessToken: accessToken,
      );

      if (uploadUrlResponse == null) {
        throw Exception('Failed to get upload URL');
      }

      final mediaId = uploadUrlResponse['mediaId'] as String;
      final uploadUrl = uploadUrlResponse['uploadUrl'] as String;

      developer.log('AwsMediaService: Got upload URL for media ID: $mediaId');

      // Step 2: Upload file directly to R2
      final uploadSuccess = await _uploadToR2(
        file: file,
        uploadUrl: uploadUrl,
        fileType: fileType,
      );

      if (!uploadSuccess) {
        throw Exception('Failed to upload file to R2');
      }

      developer.log('AwsMediaService: File uploaded successfully to R2');

      // Step 3: Update media status to 'uploaded'
      final statusUpdateSuccess = await _updateMediaStatus(
        mediaId: mediaId,
        status: 'uploaded',
        accessToken: accessToken,
      );

      if (!statusUpdateSuccess) {
        developer.log('AwsMediaService: Warning - failed to update media status');
        // Don't throw error here as the file was uploaded successfully
      }

      developer.log('AwsMediaService: Media upload completed successfully');
      return mediaId;

    } catch (e) {
      developer.log('AwsMediaService: Error uploading media: $e');
      AppLogger.error('Error uploading media', error: e);
      return null;
    }
  }

  /// Request upload URL from backend
  Future<Map<String, dynamic>?> _requestUploadUrl({
    required String fileName,
    required String fileType,
    required int fileSize,
    required String mediaType,
    required String accessToken,
  }) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/media/upload',
        body: {
          'fileName': fileName,
          'fileType': fileType,
          'fileSize': fileSize,
          'mediaType': mediaType,
        },
        accessToken: accessToken,
      );

      final data = ApiService.instance.parseResponse(response);
      return data;

    } catch (e) {
      developer.log('AwsMediaService: Error requesting upload URL: $e');
      return null;
    }
  }

  /// Upload file directly to R2 using presigned URL
  Future<bool> _uploadToR2({
    required File file,
    required String uploadUrl,
    required String fileType,
  }) async {
    try {
      final fileBytes = await file.readAsBytes();

      final response = await http.put(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': fileType,
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );

      if (response.statusCode == 200) {
        developer.log('AwsMediaService: R2 upload successful');
        return true;
      } else {
        developer.log('AwsMediaService: R2 upload failed with status: ${response.statusCode}');
        developer.log('AwsMediaService: R2 upload response: ${response.body}');
        return false;
      }

    } catch (e) {
      developer.log('AwsMediaService: Error uploading to R2: $e');
      return false;
    }
  }

  /// Update media status in backend
  Future<bool> _updateMediaStatus({
    required String mediaId,
    required String status,
    required String accessToken,
  }) async {
    try {
      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/media/$mediaId',
        body: {'status': status},
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsMediaService: Media status updated to $status');
      return true;

    } catch (e) {
      developer.log('AwsMediaService: Error updating media status: $e');
      return false;
    }
  }

  /// Get media information
  Future<Map<String, dynamic>?> getMedia(String mediaId) async {
    try {
      developer.log('AwsMediaService: Getting media info for $mediaId');

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/media/$mediaId',
      );

      final data = ApiService.instance.parseResponse(response);
      return data['media'] as Map<String, dynamic>?;

    } catch (e) {
      developer.log('AwsMediaService: Error getting media: $e');
      AppLogger.error('Error getting media', error: e);
      return null;
    }
  }

  /// Delete media
  Future<bool> deleteMedia(String mediaId) async {
    try {
      developer.log('AwsMediaService: Deleting media $mediaId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'DELETE',
        path: '/media/$mediaId',
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsMediaService: Media deleted successfully');
      return true;

    } catch (e) {
      developer.log('AwsMediaService: Error deleting media: $e');
      AppLogger.error('Error deleting media', error: e);
      return false;
    }
  }
}
